// 应用状态管理
class AppState {
  constructor() {
    this.currentProject = null;
    this.projects = this.loadProjects();
    this.rules = this.loadRules();
    this.scanResults = [];
    this.isScanning = false;
    this.settings = this.loadSettings();
  }

  loadProjects() {
    const saved = localStorage.getItem('dsk_projects');
    return saved ? JSON.parse(saved) : [];
  }

  saveProjects() {
    localStorage.setItem('dsk_projects', JSON.stringify(this.projects));
  }

  loadRules() {
    const saved = localStorage.getItem('dsk_rules');
    return saved ? JSON.parse(saved) : this.getDefaultRules();
  }

  saveRules() {
    localStorage.setItem('dsk_rules', JSON.stringify(this.rules));
  }

  loadSettings() {
    const saved = localStorage.getItem('dsk_settings');
    return saved ? JSON.parse(saved) : this.getDefaultSettings();
  }

  saveSettings() {
    localStorage.setItem('dsk_settings', JSON.stringify(this.settings));
  }

  getDefaultRules() {
    return [
      {
        id: 'email',
        name: '邮箱地址',
        regex: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}',
        type: 'email',
        scope: 'any',
        engine: 'dfa',
        sensitivity: 'medium',
        enabled: true,
        color: 'green'
      },
      {
        id: 'phone',
        name: '手机号码',
        regex: '1[3-9]\\d{9}',
        type: 'phone',
        scope: 'any',
        engine: 'dfa',
        sensitivity: 'high',
        enabled: true,
        color: 'red'
      },
      {
        id: 'id-card',
        name: '身份证号',
        regex: '[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]',
        type: 'id-card',
        scope: 'any',
        engine: 'dfa',
        sensitivity: 'high',
        enabled: true,
        color: 'red'
      },
      {
        id: 'api-key',
        name: 'API密钥',
        regex: '(api[_-]?key|access[_-]?token|secret[_-]?key)\\s*[:=]\\s*["\']?([a-zA-Z0-9_-]{20,})["\']?',
        type: 'api-key',
        scope: 'any',
        engine: 'regex',
        sensitivity: 'high',
        enabled: true,
        color: 'orange'
      }
    ];
  }

  getDefaultSettings() {
    return {
      enableHttpHandler: false,
      fileSizeLimit: 0,
      scopes: {
        suite: true,
        target: true,
        proxy: true,
        scanner: true
      },
      excludeExtensions: ['3gp', '7z', 'aac', 'abw', 'gif', 'arc', 'au', 'avi', 'azw', 'bat', 'bin'],
      darkMode: false,
      language: 'zh-CN'
    };
  }
}

// 全局应用状态
const appState = new AppState();

// 标签页管理
class TabManager {
  constructor() {
    this.currentTab = 'project';
    this.initTabs();
  }

  initTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanels = document.querySelectorAll('.tab-panel');

    tabButtons.forEach(button => {
      button.addEventListener('click', () => {
        const tabId = button.dataset.tab;
        this.switchTab(tabId);
      });
    });
  }

  switchTab(tabId) {
    // 更新按钮状态
    document.querySelectorAll('.tab-button').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

    // 更新面板显示
    document.querySelectorAll('.tab-panel').forEach(panel => {
      panel.classList.remove('active');
    });
    document.getElementById(`${tabId}-panel`).classList.add('active');

    this.currentTab = tabId;

    // 根据标签页加载相应内容
    this.loadTabContent(tabId);
  }

  loadTabContent(tabId) {
    switch (tabId) {
      case 'project':
        projectManager.renderProjects();
        break;
      case 'rules':
        rulesManager.renderRules();
        break;
      case 'results':
        resultsManager.renderResults();
        break;
    }
  }
}

// 项目管理器
class ProjectManager {
  constructor() {
    this.initEventListeners();
  }

  initEventListeners() {
    // 创建项目按钮
    document.getElementById('create-project-btn').addEventListener('click', () => {
      this.showCreateProjectModal();
    });

    // 模态框事件
    document.getElementById('cancel-create-project').addEventListener('click', () => {
      this.hideCreateProjectModal();
    });

    document.getElementById('confirm-create-project').addEventListener('click', () => {
      this.createProject();
    });
  }

  showCreateProjectModal() {
    document.getElementById('create-project-modal').classList.remove('hidden');
    document.getElementById('create-project-modal').classList.add('flex');
  }

  hideCreateProjectModal() {
    document.getElementById('create-project-modal').classList.add('hidden');
    document.getElementById('create-project-modal').classList.remove('flex');
    // 清空输入
    document.getElementById('project-name-input').value = '';
    document.getElementById('project-description-input').value = '';
  }

  createProject() {
    const name = document.getElementById('project-name-input').value.trim();
    const description = document.getElementById('project-description-input').value.trim();

    if (!name) {
      alert('请输入项目名称');
      return;
    }

    const project = {
      id: Date.now().toString(),
      name,
      description,
      createdAt: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      scanCount: 0,
      lastScanResults: null
    };

    appState.projects.push(project);
    appState.saveProjects();
    this.hideCreateProjectModal();
    this.renderProjects();
  }

  renderProjects() {
    const grid = document.getElementById('projects-grid');
    grid.innerHTML = '';

    if (appState.projects.length === 0) {
      grid.innerHTML = `
        <div class="col-span-full text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无项目</h3>
          <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">创建您的第一个扫描项目</p>
        </div>
      `;
      return;
    }

    appState.projects.forEach(project => {
      const projectCard = document.createElement('div');
      projectCard.className = `project-card ${appState.currentProject?.id === project.id ? 'active' : ''}`;
      projectCard.innerHTML = `
        <h3>${project.name}</h3>
        <p>${project.description || '暂无描述'}</p>
        <div class="project-meta">
          <span>扫描次数: ${project.scanCount}</span>
          <span>${new Date(project.createdAt).toLocaleDateString()}</span>
        </div>
      `;

      projectCard.addEventListener('click', () => {
        this.selectProject(project);
      });

      grid.appendChild(projectCard);
    });
  }

  selectProject(project) {
    appState.currentProject = project;
    this.renderProjects();

    // 切换到扫描器标签
    tabManager.switchTab('scanner');
  }
}

// 规则管理器
class RulesManager {
  constructor() {
    this.initEventListeners();
  }

  initEventListeners() {
    // 添加规则按钮
    document.getElementById('add-rule-btn').addEventListener('click', () => {
      this.showAddRuleModal();
    });

    // 模态框事件
    document.getElementById('cancel-add-rule').addEventListener('click', () => {
      this.hideAddRuleModal();
    });

    document.getElementById('confirm-add-rule').addEventListener('click', () => {
      this.addRule();
    });

    document.getElementById('test-regex-btn').addEventListener('click', () => {
      this.testRegex();
    });

    // 全选规则
    document.getElementById('enable-all-rules').addEventListener('change', (e) => {
      this.toggleAllRules(e.target.checked);
    });

    // 规则类型变化时更新正则表达式
    document.getElementById('rule-type-select').addEventListener('change', (e) => {
      this.updateRegexByType(e.target.value);
    });
  }

  showAddRuleModal() {
    document.getElementById('add-rule-modal').classList.remove('hidden');
    document.getElementById('add-rule-modal').classList.add('flex');
  }

  hideAddRuleModal() {
    document.getElementById('add-rule-modal').classList.add('hidden');
    document.getElementById('add-rule-modal').classList.remove('flex');
    // 清空输入
    document.getElementById('rule-name-input').value = '';
    document.getElementById('rule-regex-input').value = '';
    document.getElementById('rule-test-input').value = '';
    document.getElementById('regex-test-result').innerHTML = '';
  }

  updateRegexByType(type) {
    const regexInput = document.getElementById('rule-regex-input');
    const nameInput = document.getElementById('rule-name-input');

    const regexTemplates = {
      email: {
        name: '邮箱地址检测',
        regex: '[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}'
      },
      phone: {
        name: '手机号码检测',
        regex: '1[3-9]\\d{9}'
      },
      'id-card': {
        name: '身份证号检测',
        regex: '[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]'
      },
      'credit-card': {
        name: '信用卡号检测',
        regex: '\\b(?:\\d{4}[-\\s]?){3}\\d{4}\\b'
      },
      'api-key': {
        name: 'API密钥检测',
        regex: '(api[_-]?key|access[_-]?token|secret[_-]?key)\\s*[:=]\\s*["\']?([a-zA-Z0-9_-]{20,})["\']?'
      },
      password: {
        name: '密码字段检测',
        regex: '(password|pwd|pass)\\s*[:=]\\s*["\']?([^\\s"\']{6,})["\']?'
      }
    };

    if (regexTemplates[type]) {
      nameInput.value = regexTemplates[type].name;
      regexInput.value = regexTemplates[type].regex;
    }
  }

  testRegex() {
    const regex = document.getElementById('rule-regex-input').value;
    const testText = document.getElementById('rule-test-input').value;
    const resultDiv = document.getElementById('regex-test-result');

    if (!regex || !testText) {
      resultDiv.innerHTML = '<span class="text-yellow-600">请输入正则表达式和测试文本</span>';
      return;
    }

    try {
      const regexObj = new RegExp(regex, 'gi');
      const matches = testText.match(regexObj);

      if (matches) {
        resultDiv.innerHTML = `
          <span class="text-green-600">✓ 匹配成功</span>
          <div class="mt-1 text-xs text-gray-600 dark:text-gray-400">
            找到 ${matches.length} 个匹配: ${matches.join(', ')}
          </div>
        `;
      } else {
        resultDiv.innerHTML = '<span class="text-red-600">✗ 无匹配结果</span>';
      }
    } catch (error) {
      resultDiv.innerHTML = `<span class="text-red-600">✗ 正则表达式错误: ${error.message}</span>`;
    }
  }

  addRule() {
    const name = document.getElementById('rule-name-input').value.trim();
    const regex = document.getElementById('rule-regex-input').value.trim();
    const type = document.getElementById('rule-type-select').value;
    const scope = document.getElementById('rule-scope-select').value;
    const engine = document.getElementById('rule-engine-select').value;
    const sensitivity = document.getElementById('rule-sensitivity-select').value;

    if (!name || !regex) {
      alert('请输入规则名称和正则表达式');
      return;
    }

    // 验证正则表达式
    try {
      new RegExp(regex);
    } catch (error) {
      alert('正则表达式格式错误: ' + error.message);
      return;
    }

    const rule = {
      id: Date.now().toString(),
      name,
      regex,
      type,
      scope,
      engine,
      sensitivity,
      enabled: true,
      color: this.getSensitivityColor(sensitivity)
    };

    appState.rules.push(rule);
    appState.saveRules();
    this.hideAddRuleModal();
    this.renderRules();
  }

  getSensitivityColor(sensitivity) {
    const colors = {
      high: 'red',
      medium: 'orange',
      low: 'green'
    };
    return colors[sensitivity] || 'green';
  }

  toggleAllRules(enabled) {
    appState.rules.forEach(rule => {
      rule.enabled = enabled;
    });
    appState.saveRules();
    this.renderRules();
  }

  renderRules() {
    const tbody = document.getElementById('rules-table-body');
    tbody.innerHTML = '';

    if (appState.rules.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="8" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
            暂无检测规则，请添加规则
          </td>
        </tr>
      `;
      return;
    }

    appState.rules.forEach(rule => {
      const row = document.createElement('tr');
      row.className = `rule-row ${!rule.enabled ? 'disabled' : ''}`;
      row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap">
          <input type="checkbox" ${rule.enabled ? 'checked' : ''}
                 class="rule-enabled-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                 data-rule-id="${rule.id}">
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
          ${rule.name}
        </td>
        <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400 font-mono max-w-xs truncate">
          ${rule.regex}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${rule.type}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${rule.scope}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${rule.engine}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="risk-badge ${rule.sensitivity}">${rule.sensitivity}</span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="rulesManager.editRule('${rule.id}')">编辑</button>
          <button class="text-red-600 hover:text-red-900" onclick="rulesManager.deleteRule('${rule.id}')">删除</button>
        </td>
      `;

      // 添加启用/禁用事件监听
      const checkbox = row.querySelector('.rule-enabled-checkbox');
      checkbox.addEventListener('change', (e) => {
        this.toggleRule(rule.id, e.target.checked);
      });

      tbody.appendChild(row);
    });
  }

  toggleRule(ruleId, enabled) {
    const rule = appState.rules.find(r => r.id === ruleId);
    if (rule) {
      rule.enabled = enabled;
      appState.saveRules();
      this.renderRules();
    }
  }

  editRule(ruleId) {
    // TODO: 实现编辑规则功能
    console.log('编辑规则:', ruleId);
  }

  deleteRule(ruleId) {
    if (confirm('确定要删除这个规则吗？')) {
      appState.rules = appState.rules.filter(r => r.id !== ruleId);
      appState.saveRules();
      this.renderRules();
    }
  }
}

// 扫描管理器
class ScanManager {
  constructor() {
    this.selectedFiles = [];
    this.selectedFolders = [];
    this.initEventListeners();
  }

  initEventListeners() {
    document.getElementById('select-files-btn').addEventListener('click', () => {
      this.selectFiles();
    });

    document.getElementById('select-folder-btn').addEventListener('click', () => {
      this.selectFolder();
    });

    document.getElementById('start-scan-btn').addEventListener('click', () => {
      this.startScan();
    });
  }

  async selectFiles() {
    // 模拟文件选择（在实际应用中会使用Tauri的文件对话框）
    const mockFiles = [
      '/path/to/file1.js',
      '/path/to/file2.py',
      '/path/to/config.json'
    ];

    this.selectedFiles = mockFiles;
    this.updateSelectedTargets();
  }

  async selectFolder() {
    // 模拟文件夹选择
    const mockFolder = '/path/to/project';
    this.selectedFolders = [mockFolder];
    this.updateSelectedTargets();
  }

  updateSelectedTargets() {
    const targetList = document.getElementById('target-list');
    const selectedTargets = document.getElementById('selected-targets');

    if (this.selectedFiles.length === 0 && this.selectedFolders.length === 0) {
      selectedTargets.classList.add('hidden');
      return;
    }

    selectedTargets.classList.remove('hidden');
    targetList.innerHTML = '';

    [...this.selectedFiles, ...this.selectedFolders].forEach(target => {
      const li = document.createElement('li');
      li.className = 'flex items-center justify-between text-sm text-gray-700 dark:text-gray-300';
      li.innerHTML = `
        <span class="truncate">${target}</span>
        <button class="text-red-500 hover:text-red-700 ml-2" onclick="scanManager.removeTarget('${target}')">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      `;
      targetList.appendChild(li);
    });
  }

  removeTarget(target) {
    this.selectedFiles = this.selectedFiles.filter(f => f !== target);
    this.selectedFolders = this.selectedFolders.filter(f => f !== target);
    this.updateSelectedTargets();
  }

  async startScan() {
    if (this.selectedFiles.length === 0 && this.selectedFolders.length === 0) {
      alert('请先选择要扫描的文件或文件夹');
      return;
    }

    if (!appState.currentProject) {
      alert('请先选择一个项目');
      return;
    }

    const enabledRules = appState.rules.filter(rule => rule.enabled);
    if (enabledRules.length === 0) {
      alert('请至少启用一个检测规则');
      return;
    }

    appState.isScanning = true;
    this.showScanProgress();

    try {
      // 模拟扫描过程
      await this.performScan(enabledRules);

      // 更新项目扫描次数
      appState.currentProject.scanCount++;
      appState.currentProject.lastModified = new Date().toISOString();
      appState.saveProjects();

      // 切换到结果页面
      tabManager.switchTab('results');
    } catch (error) {
      alert('扫描过程中出现错误: ' + error.message);
    } finally {
      appState.isScanning = false;
      this.hideScanProgress();
    }
  }

  showScanProgress() {
    document.getElementById('scan-progress').classList.remove('hidden');
    document.getElementById('start-scan-btn').disabled = true;
    document.getElementById('start-scan-btn').classList.add('opacity-50', 'cursor-not-allowed');
  }

  hideScanProgress() {
    document.getElementById('scan-progress').classList.add('hidden');
    document.getElementById('start-scan-btn').disabled = false;
    document.getElementById('start-scan-btn').classList.remove('opacity-50', 'cursor-not-allowed');
  }

  async performScan(rules) {
    const mockResults = [];
    const totalFiles = 10; // 模拟文件数量

    for (let i = 0; i < totalFiles; i++) {
      // 更新进度
      const progress = ((i + 1) / totalFiles) * 100;
      document.getElementById('progress-bar').style.width = `${progress}%`;
      document.getElementById('progress-text').textContent = `${Math.round(progress)}%`;
      document.getElementById('current-file').textContent = `正在扫描: file${i + 1}.js`;

      // 模拟扫描延迟
      await new Promise(resolve => setTimeout(resolve, 200));

      // 模拟发现敏感信息
      if (Math.random() > 0.7) {
        const rule = rules[Math.floor(Math.random() * rules.length)];
        mockResults.push({
          id: Date.now() + Math.random(),
          file: `file${i + 1}.js`,
          line: Math.floor(Math.random() * 100) + 1,
          rule: rule.name,
          match: this.generateMockMatch(rule.type),
          riskLevel: rule.sensitivity,
          timestamp: new Date().toISOString()
        });
      }
    }

    appState.scanResults = mockResults;
    return mockResults;
  }

  generateMockMatch(ruleType) {
    const mockData = {
      email: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      phone: ['13812345678', '15987654321', '18666888999'],
      'id-card': ['110101199001011234', '320102198505050987'],
      'api-key': ['sk-1234567890abcdef', 'api_key_abcdef123456'],
      password: ['password123', 'admin123', 'secret_key']
    };

    const matches = mockData[ruleType] || ['sensitive_data'];
    return matches[Math.floor(Math.random() * matches.length)];
  }
}

// 结果管理器
class ResultsManager {
  constructor() {
    this.filteredResults = [];
    this.initEventListeners();
  }

  initEventListeners() {
    document.getElementById('search-results').addEventListener('input', (e) => {
      this.filterResults();
    });

    document.getElementById('filter-risk-level').addEventListener('change', (e) => {
      this.filterResults();
    });

    document.getElementById('export-results-btn').addEventListener('click', () => {
      this.exportResults();
    });
  }

  renderResults() {
    this.filteredResults = [...appState.scanResults];
    this.updateStatistics();
    this.renderResultsTable();
  }

  updateStatistics() {
    const results = appState.scanResults;
    const highRisk = results.filter(r => r.riskLevel === 'high').length;
    const mediumRisk = results.filter(r => r.riskLevel === 'medium').length;
    const lowRisk = results.filter(r => r.riskLevel === 'low').length;
    const scannedFiles = new Set(results.map(r => r.file)).size;

    document.getElementById('high-risk-count').textContent = highRisk;
    document.getElementById('medium-risk-count').textContent = mediumRisk;
    document.getElementById('low-risk-count').textContent = lowRisk;
    document.getElementById('scanned-files-count').textContent = scannedFiles;
  }

  filterResults() {
    const searchTerm = document.getElementById('search-results').value.toLowerCase();
    const riskFilter = document.getElementById('filter-risk-level').value;

    this.filteredResults = appState.scanResults.filter(result => {
      const matchesSearch = !searchTerm ||
        result.file.toLowerCase().includes(searchTerm) ||
        result.rule.toLowerCase().includes(searchTerm) ||
        result.match.toLowerCase().includes(searchTerm);

      const matchesRisk = !riskFilter || result.riskLevel === riskFilter;

      return matchesSearch && matchesRisk;
    });

    this.renderResultsTable();
  }

  renderResultsTable() {
    const tbody = document.getElementById('results-table-body');
    tbody.innerHTML = '';

    if (this.filteredResults.length === 0) {
      tbody.innerHTML = `
        <tr>
          <td colspan="6" class="px-6 py-12 text-center text-gray-500 dark:text-gray-400">
            ${appState.scanResults.length === 0 ? '暂无扫描结果' : '没有符合条件的结果'}
          </td>
        </tr>
      `;
      return;
    }

    this.filteredResults.forEach(result => {
      const row = document.createElement('tr');
      row.className = 'hover:bg-gray-50 dark:hover:bg-gray-700';
      row.innerHTML = `
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
          ${result.file}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${result.line}
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
          ${result.rule}
        </td>
        <td class="px-6 py-4 text-sm text-gray-900 dark:text-white font-mono max-w-xs truncate">
          ${result.match}
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
          <span class="risk-badge ${result.riskLevel}">${result.riskLevel}</span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
          <button class="text-blue-600 hover:text-blue-900 mr-3" onclick="resultsManager.viewDetail('${result.id}')">查看</button>
          <button class="text-red-600 hover:text-red-900" onclick="resultsManager.ignoreResult('${result.id}')">忽略</button>
        </td>
      `;
      tbody.appendChild(row);
    });
  }

  viewDetail(resultId) {
    const result = appState.scanResults.find(r => r.id == resultId);
    if (result) {
      alert(`文件: ${result.file}\n行号: ${result.line}\n规则: ${result.rule}\n匹配内容: ${result.match}`);
    }
  }

  ignoreResult(resultId) {
    if (confirm('确定要忽略这个结果吗？')) {
      appState.scanResults = appState.scanResults.filter(r => r.id != resultId);
      this.renderResults();
    }
  }

  exportResults() {
    const data = JSON.stringify(appState.scanResults, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `scan_results_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}

// 设置管理器
class SettingsManager {
  constructor() {
    this.initEventListeners();
    this.loadSettings();
  }

  initEventListeners() {
    document.getElementById('save-settings-btn').addEventListener('click', () => {
      this.saveSettings();
    });

    document.getElementById('reset-settings-btn').addEventListener('click', () => {
      this.resetSettings();
    });

    document.getElementById('theme-toggle').addEventListener('click', () => {
      this.toggleTheme();
    });

    document.getElementById('dark-mode-toggle').addEventListener('change', (e) => {
      this.setTheme(e.target.checked ? 'dark' : 'light');
    });
  }

  loadSettings() {
    const settings = appState.settings;

    document.getElementById('enable-http-handler').checked = settings.enableHttpHandler;
    document.getElementById('file-size-limit').value = settings.fileSizeLimit;
    document.getElementById('scope-suite').checked = settings.scopes.suite;
    document.getElementById('scope-target').checked = settings.scopes.target;
    document.getElementById('scope-proxy').checked = settings.scopes.proxy;
    document.getElementById('scope-scanner').checked = settings.scopes.scanner;
    document.getElementById('exclude-extensions').value = settings.excludeExtensions.join('\n');
    document.getElementById('dark-mode-toggle').checked = settings.darkMode;
    document.getElementById('language-select').value = settings.language;
  }

  saveSettings() {
    appState.settings = {
      enableHttpHandler: document.getElementById('enable-http-handler').checked,
      fileSizeLimit: parseInt(document.getElementById('file-size-limit').value),
      scopes: {
        suite: document.getElementById('scope-suite').checked,
        target: document.getElementById('scope-target').checked,
        proxy: document.getElementById('scope-proxy').checked,
        scanner: document.getElementById('scope-scanner').checked
      },
      excludeExtensions: document.getElementById('exclude-extensions').value.split('\n').filter(ext => ext.trim()),
      darkMode: document.getElementById('dark-mode-toggle').checked,
      language: document.getElementById('language-select').value
    };

    appState.saveSettings();
    alert('设置已保存');
  }

  resetSettings() {
    if (confirm('确定要重置所有设置吗？')) {
      appState.settings = appState.getDefaultSettings();
      appState.saveSettings();
      this.loadSettings();
      alert('设置已重置');
    }
  }

  toggleTheme() {
    const isDark = document.documentElement.classList.contains('dark');
    this.setTheme(isDark ? 'light' : 'dark');
  }

  setTheme(theme) {
    if (theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }

    appState.settings.darkMode = theme === 'dark';
    appState.saveSettings();
  }
}

// 全局管理器实例
let tabManager;
let projectManager;
let rulesManager;
let scanManager;
let resultsManager;
let settingsManager;

// 应用初始化
document.addEventListener('DOMContentLoaded', () => {
  // 初始化管理器
  tabManager = new TabManager();
  projectManager = new ProjectManager();
  rulesManager = new RulesManager();
  scanManager = new ScanManager();
  resultsManager = new ResultsManager();
  settingsManager = new SettingsManager();

  // 应用主题
  if (appState.settings.darkMode) {
    document.documentElement.classList.add('dark');
  }

  // 初始化内容
  projectManager.renderProjects();
  rulesManager.renderRules();
  resultsManager.renderResults();

  // 模拟内存使用更新
  setInterval(() => {
    const memoryUsage = (Math.random() * 0.5 + 1.0).toFixed(2) + 'GB';
    document.getElementById('memory-usage').textContent = memoryUsage;
  }, 5000);

  console.log('DSK 敏感信息扫描工具已初始化');
});

// 全局函数（供HTML中的onclick使用）
window.rulesManager = rulesManager;
window.resultsManager = resultsManager;
window.scanManager = scanManager;
