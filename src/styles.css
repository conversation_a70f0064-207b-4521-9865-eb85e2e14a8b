/* 基础样式重置和全局设置 */
* {
  box-sizing: border-box;
}

:root {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  font-weight: 400;
  color: #1f2937;
  background-color: #f9fafb;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 标签按钮样式 */
.tab-button {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
}

.tab-button:hover {
  color: #374151;
  background-color: #f3f4f6;
}

.tab-button.active {
  color: #2563eb;
  border-bottom-color: #2563eb;
  background-color: #eff6ff;
}

/* 面板样式 */
.tab-panel {
  display: none;
  flex: 1;
  overflow-y: auto;
}

.tab-panel.active {
  display: block;
}

/* 项目卡片样式 */
.project-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.project-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.project-card.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.project-card h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.project-card p {
  margin: 0 0 12px 0;
  color: #6b7280;
  font-size: 14px;
}

.project-card .project-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #9ca3af;
}

/* 规则表格样式 */
.rule-row {
  transition: background-color 0.2s ease;
}

.rule-row:hover {
  background-color: #f9fafb;
}

.rule-row.disabled {
  opacity: 0.5;
}

.rule-enabled-checkbox {
  transform: scale(1.1);
}

/* 风险级别标签 */
.risk-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.risk-badge.high {
  background-color: #fef2f2;
  color: #dc2626;
}

.risk-badge.medium {
  background-color: #fffbeb;
  color: #d97706;
}

.risk-badge.low {
  background-color: #f0f9ff;
  color: #0284c7;
}

/* 扫描进度动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.scanning {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 模态框样式 */
.modal-overlay {
  backdrop-filter: blur(4px);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 深色模式 */
@media (prefers-color-scheme: dark) {
  :root {
    color: #f9fafb;
    background-color: #111827;
  }

  .project-card {
    background: #1f2937;
    border-color: #374151;
  }

  .project-card:hover {
    border-color: #3b82f6;
    background-color: #1e3a8a;
  }

  .project-card.active {
    background-color: #1e3a8a;
  }

  .project-card h3 {
    color: #f9fafb;
  }

  .project-card p {
    color: #d1d5db;
  }

  .tab-button {
    color: #d1d5db;
  }

  .tab-button:hover {
    color: #f9fafb;
    background-color: #374151;
  }

  .tab-button.active {
    color: #60a5fa;
    background-color: #1e3a8a;
  }

  .rule-row:hover {
    background-color: #374151;
  }

  ::-webkit-scrollbar-track {
    background: #374151;
  }

  ::-webkit-scrollbar-thumb {
    background: #6b7280;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-button {
    padding: 8px 12px;
    font-size: 12px;
  }

  .tab-button svg {
    width: 16px;
    height: 16px;
  }

  .project-card {
    padding: 16px;
  }

  .grid {
    grid-template-columns: 1fr;
  }
}
